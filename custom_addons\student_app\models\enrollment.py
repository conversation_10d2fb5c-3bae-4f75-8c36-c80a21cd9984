# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError


class StudentEnrollment(models.Model):
    _name = 'student.enrollment'
    _description = 'Student Course Enrollment'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'enrollment_date desc'
    _rec_name = 'display_name'

    # Basic Information
    student_id = fields.Many2one(
        'student.student',
        string='Student',
        required=True,
        ondelete='cascade',
        tracking=True
    )
    
    course_id = fields.Many2one(
        'student.course',
        string='Course',
        required=True,
        ondelete='cascade',
        tracking=True
    )
    
    enrollment_date = fields.Date(
        string='Enrollment Date',
        default=fields.Date.today,
        required=True,
        tracking=True
    )
    
    # Status and Progress
    status = fields.Selection([  # type: ignore
        ('enrolled', 'Enrolled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('dropped', 'Dropped'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='enrolled', required=True, tracking=True)
    
    # Grades and Assessment
    midterm_grade = fields.Float(
        string='Midterm Grade',
        digits=(5, 2),
        help="Midterm examination grade (0-100)"
    )
    
    final_grade = fields.Float(
        string='Final Grade',
        digits=(5, 2),
        help="Final examination grade (0-100)"
    )
    
    assignment_grade = fields.Float(
        string='Assignment Grade',
        digits=(5, 2),
        help="Average assignment grade (0-100)"
    )
    
    participation_grade = fields.Float(
        string='Participation Grade',
        digits=(5, 2),
        help="Class participation grade (0-100)"
    )
    
    overall_grade = fields.Float(
        string='Overall Grade',
        compute='_compute_overall_grade',
        store=True,
        digits=(5, 2),
        help="Calculated overall grade"
    )
    
    letter_grade = fields.Selection([  # type: ignore
        ('A+', 'A+ (97-100)'),
        ('A', 'A (93-96)'),
        ('A-', 'A- (90-92)'),
        ('B+', 'B+ (87-89)'),
        ('B', 'B (83-86)'),
        ('B-', 'B- (80-82)'),
        ('C+', 'C+ (77-79)'),
        ('C', 'C (73-76)'),
        ('C-', 'C- (70-72)'),
        ('D+', 'D+ (67-69)'),
        ('D', 'D (63-66)'),
        ('D-', 'D- (60-62)'),
        ('F', 'F (0-59)')
    ], string='Letter Grade', compute='_compute_letter_grade', store=True)
    
    grade_points = fields.Float(
        string='Grade Points',
        compute='_compute_grade_points',
        store=True,
        digits=(3, 2),
        help="Grade points for GPA calculation"
    )
    
    # Attendance
    total_classes = fields.Integer(
        string='Total Classes',
        default=0,
        help="Total number of classes in the course"
    )
    
    attended_classes = fields.Integer(
        string='Attended Classes',
        default=0,
        help="Number of classes attended by student"
    )
    
    attendance_percentage = fields.Float(
        string='Attendance %',
        compute='_compute_attendance_percentage',
        store=True,
        digits=(5, 2),
        help="Attendance percentage"
    )
    
    # Additional Information
    notes = fields.Text(string='Notes')
    completion_date = fields.Date(string='Completion Date')
    
    # Display name for record representation
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    # Related fields for easy access
    student_name = fields.Char(related='student_id.name', string='Student Name', store=True)
    course_name = fields.Char(related='course_id.name', string='Course Name', store=True)
    course_code = fields.Char(related='course_id.code', string='Course Code', store=True)
    course_credits = fields.Integer(related='course_id.credits', string='Credits', store=True)
    
    # Computed Fields
    @api.depends('student_id', 'course_id')
    def _compute_display_name(self):
        """Compute display name for enrollment record"""
        for enrollment in self:
            if enrollment.student_id and enrollment.course_id:
                enrollment.display_name = f"{enrollment.student_id.name} - {enrollment.course_id.name}"
            else:
                enrollment.display_name = "New Enrollment"
    
    @api.depends('midterm_grade', 'final_grade', 'assignment_grade', 'participation_grade')
    def _compute_overall_grade(self):
        """Compute overall grade based on weighted average"""
        for enrollment in self:
            # Weights: Midterm 25%, Final 35%, Assignments 25%, Participation 15%
            grades = [
                enrollment.midterm_grade or 0,
                enrollment.final_grade or 0,
                enrollment.assignment_grade or 0,
                enrollment.participation_grade or 0
            ]
            weights = [0.25, 0.35, 0.25, 0.15]
            
            if any(grades):
                enrollment.overall_grade = sum(grade * weight for grade, weight in zip(grades, weights))
            else:
                enrollment.overall_grade = 0.0
    
    @api.depends('overall_grade')
    def _compute_letter_grade(self):
        """Compute letter grade based on overall grade"""
        for enrollment in self:
            grade = enrollment.overall_grade
            if grade >= 97:
                enrollment.letter_grade = 'A+'
            elif grade >= 93:
                enrollment.letter_grade = 'A'
            elif grade >= 90:
                enrollment.letter_grade = 'A-'
            elif grade >= 87:
                enrollment.letter_grade = 'B+'
            elif grade >= 83:
                enrollment.letter_grade = 'B'
            elif grade >= 80:
                enrollment.letter_grade = 'B-'
            elif grade >= 77:
                enrollment.letter_grade = 'C+'
            elif grade >= 73:
                enrollment.letter_grade = 'C'
            elif grade >= 70:
                enrollment.letter_grade = 'C-'
            elif grade >= 67:
                enrollment.letter_grade = 'D+'
            elif grade >= 63:
                enrollment.letter_grade = 'D'
            elif grade >= 60:
                enrollment.letter_grade = 'D-'
            else:
                enrollment.letter_grade = 'F'
    
    @api.depends('letter_grade')
    def _compute_grade_points(self):
        """Compute grade points for GPA calculation"""
        grade_point_map = {
            'A+': 4.0, 'A': 4.0, 'A-': 3.7,
            'B+': 3.3, 'B': 3.0, 'B-': 2.7,
            'C+': 2.3, 'C': 2.0, 'C-': 1.7,
            'D+': 1.3, 'D': 1.0, 'D-': 0.7,
            'F': 0.0
        }
        for enrollment in self:
            enrollment.grade_points = grade_point_map.get(enrollment.letter_grade, 0.0)
    
    @api.depends('total_classes', 'attended_classes')
    def _compute_attendance_percentage(self):
        """Compute attendance percentage"""
        for enrollment in self:
            if enrollment.total_classes > 0:
                enrollment.attendance_percentage = (enrollment.attended_classes / enrollment.total_classes) * 100
            else:
                enrollment.attendance_percentage = 0.0
    
    # Constraints and Validations
    @api.constrains('student_id', 'course_id')
    def _check_unique_enrollment(self):
        """Ensure student can only enroll once per course"""
        for enrollment in self:
            existing = self.search([
                ('student_id', '=', enrollment.student_id.id),
                ('course_id', '=', enrollment.course_id.id),
                ('id', '!=', enrollment.id),
                ('status', 'not in', ['dropped', 'cancelled'])
            ])
            if existing:
                raise ValidationError(
                    f"Student {enrollment.student_id.name} is already enrolled in course {enrollment.course_id.name}."
                )
    
    @api.constrains('midterm_grade', 'final_grade', 'assignment_grade', 'participation_grade')
    def _check_grade_range(self):
        """Validate grade ranges"""
        for enrollment in self:
            grades = [
                ('Midterm', enrollment.midterm_grade),
                ('Final', enrollment.final_grade),
                ('Assignment', enrollment.assignment_grade),
                ('Participation', enrollment.participation_grade)
            ]
            for grade_name, grade_value in grades:
                if grade_value and (grade_value < 0 or grade_value > 100):
                    raise ValidationError(f"{grade_name} grade must be between 0 and 100.")
    
    @api.constrains('attended_classes', 'total_classes')
    def _check_attendance(self):
        """Validate attendance numbers"""
        for enrollment in self:
            if enrollment.attended_classes < 0:
                raise ValidationError("Attended classes cannot be negative.")
            if enrollment.total_classes < 0:
                raise ValidationError("Total classes cannot be negative.")
            if enrollment.attended_classes > enrollment.total_classes:
                raise ValidationError("Attended classes cannot exceed total classes.")
    
    # Methods
    @api.model
    def create(self, vals):
        """Override create to check course capacity"""
        if vals.get('course_id'):
            course = self.env['student.course'].browse(vals['course_id'])
            if not course.check_enrollment_capacity():
                raise UserError(f"Course {course.name} is at full capacity!")
        return super(StudentEnrollment, self).create(vals)
    
    def action_complete(self):
        """Mark enrollment as completed"""
        for enrollment in self:
            enrollment.status = 'completed'
            enrollment.completion_date = fields.Date.today()
            enrollment.message_post(
                body=f"Enrollment completed with grade: {enrollment.letter_grade} ({enrollment.overall_grade:.2f})"
            )
    
    def action_drop(self):
        """Drop the enrollment"""
        for enrollment in self:
            enrollment.status = 'dropped'
            enrollment.message_post(body="Student dropped from the course.")
    
    def action_fail(self):
        """Mark enrollment as failed"""
        for enrollment in self:
            enrollment.status = 'failed'
            enrollment.completion_date = fields.Date.today()
            enrollment.message_post(body="Student failed the course.")
    
    def update_attendance(self, attended, total):
        """Update attendance information"""
        self.ensure_one()
        self.attended_classes = attended
        self.total_classes = total
